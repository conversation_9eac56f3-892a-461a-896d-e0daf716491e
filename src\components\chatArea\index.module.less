/* ChatArea 样式 */
.chatArea {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 初始状态 - 居中布局 */
.centerLayout {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-image: url("../../assets/chat-bg.png");
  background-size: calc(100% - 180px) calc(100% - 170px);
  background-position: center;
  background-repeat: no-repeat;

  .centerContent {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    transform: translateY(-1vh);
  }

  .welcomeText {
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    margin-bottom: 24px;

    .welcomeTitle {
      font-size: 26px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.88);
      margin-bottom: 8px;
      line-height: 34px;
    }

    .welcomeSubtitle {
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      margin: 0 auto;
    }
  }
}

.centerInput {
  width: 100%;
  max-width: 800px;
  margin-bottom: 20px;
}

.helpText {
  height: 42px;
  line-height: 42px;
  text-align: center;
  color: rgba(0, 0, 0, 0.25);
  font-size: 14px;
}

/* 有消息时的布局 */
.chatLayout {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  min-width: 900px;
}

.messagesContainer {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 140px;
  margin-top: 72px;
  width: 100%;
  position: relative; 

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(---, rgba(0, 0, 0, 0.06));
    border-radius: 6px;

    &:hover {
      background: var(---, rgba(0, 0, 0, 0.06));
    }
  }
}

.messagesList {
  width: 800px;
  min-width: 800px;
  max-width: calc(100vw - 40px); /* 确保在小屏幕上不会超出 */
  margin: 0 auto;
  /* 确保消息列表内容不会溢出 */
  overflow: hidden;
  box-sizing: border-box;
}

.loadingMore {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #64748b;
  font-size: 14px;
  gap: 8px;
}

/* 回到底部按钮样式 */
.scrollToBottomBtn {
  position: absolute;
  top: -64px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  transition: all 0.3s ease;
  cursor: pointer;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;

  &:hover {
    transform: translateX(-50%) translateY(-2px);
    box-shadow: 0 18px 36px 0 rgba(0, 0, 0, 0.08);
  }

  /* 让两个 SVG 都居中叠加 */
  svg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.fixedBottom {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 810px;
  background: white;
}
